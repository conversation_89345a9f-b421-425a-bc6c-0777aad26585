import{r as e}from"./index-Dzclihdw.js";import{F as o,S as _,D as t,_ as n,a as m,b as v,c as A,d as g,e as E,f as b,g as d,i as f}from"./index-Dzclihdw.js";var r="firebase",s="11.8.1";/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */e(r,s,"app");export{o as FirebaseError,_ as SDK_VERSION,t as _DEFAULT_ENTRY_NAME,n as _addComponent,m as _apps,v as _components,A as _getProvider,g as _isFirebaseServerApp,E as _registerComponent,b as _serverApps,d as getApp,f as initializeApp,e as registerVersion};
